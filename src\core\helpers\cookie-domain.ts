export class CookieDomain {

    private hostname: string;

    constructor() {
        this.hostname = window.location.hostname;
    }

    public getDomain(): string {
        if (this.isLocalhost() || this.isIpAddress()) {
            return this.hostname;
        }

        const subdomains = this.hostname.split('.');
        if (subdomains.length > 2) {
            return `.${subdomains.slice(-2).join(".")}`;
        }

        return this.hostname;
    }

    private isLocalhost(): boolean {
        return this.hostname === "localhost";
    }

    private isIpAddress(): boolean {
        return /^\d{1,3}(\.\d{1,3}){3}$/.test(this.hostname);
    }
}
