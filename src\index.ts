import { EventManager } from "./core/events/event-manager.ts";
import { StoreManager, type CookieParams } from "./core/store/store-manager.ts";
import { modulesProvider } from "./core/providers/modules.ts";
import { preScript } from "./core/providers/prescript.ts";
import { Logger } from "./core/helpers/logger.ts";

window.SizebayPrescript = () => ({
    getModules(): string[] {
        return ['similar', 'tryon', "complementar"];
    }
})

async function bootstrap() {
    const eventManager = new EventManager();

    const storeManager = new StoreManager();

    const initStoreManager = () => {
        const testCookie1: CookieParams = { name: 'testCookie1', value: 'testValue1', domain: 'sizebay.com.br' };
        storeManager.setCookie(testCookie1);
        const testCookie2: CookieParams = { name: 'testCookie2', value: 'testValue2' };
        storeManager.setCookie(testCookie2);
        const testCookie3: CookieParams = { name: 'testCookie3', value: 'testValue3', expires: new Date(Date.now() + **********), path: '/test3', domain: 'www.test3.com.br' };
        storeManager.setCookie(testCookie3);
        Logger.info(`All Cookies: ${document.cookie}`);
    };

    initStoreManager();

    const modulesToLoad = preScript.modules;

    await modulesProvider.load(modulesToLoad);

    eventManager.publish('app:ready', { loadedModules: modulesToLoad });
}

bootstrap().then(() => Logger.info('App inicializada com sucesso.'));
