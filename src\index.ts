import { EventManager } from "./core/events/event-manager.ts";
import { StoreManager } from "./core/store/store-manager.ts";
import { modulesProvider } from "./core/providers/modules.ts";
import { preScript } from "./core/providers/prescript.ts";
import { Logger } from "./core/helpers/logger.ts";

window.SizebayPrescript = () => ({
    getModules(): string[] {
        return ['similar', 'tryon', "complementar"];
    }
})

async function bootstrap() {
    const eventManager = new EventManager();

    const storeManager = new StoreManager();

    const initStoreManager = () => {
        storeManager.setCookie({ name: 'testCookie2', value: 'testValue2', domain: StoreManager.getDomain('sizebay.com.br') });
        storeManager.setCookie({ name: 'testCookie3', value: 'testValue3', domain: StoreManager.getDomain('www.votorantin.com.br'), path: '/votorantin' });
        Logger.info(`All Cookies: ${document.cookie}`);
    };

    initStoreManager();

    const modulesToLoad = preScript.modules;

    await modulesProvider.load(modulesToLoad);

    eventManager.publish('app:ready', { loadedModules: modulesToLoad });
}

bootstrap().then(() => Logger.info('App inicializada com sucesso.'));
